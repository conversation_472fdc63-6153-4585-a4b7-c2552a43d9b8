"use client";
import React from 'react';
import FlightCard from './FlightCard';
import AboutSection from './AboutSection';
import InflightFeatures from './InflightFeatures';
import BaggageSection from './BaggageSection';
import FareRules from './FareRules';
import SidebarWidgets from './SidebarWidgets';
import FAQ from './FAQ';
import Sidebar from './DetailSidebar';
import ReviewForm from './ReviewForm';

const FlightDetailPage = ({
  flightData = {},
  aboutData = {},
  sidebarData = {},
  className = ""
}) => {
  // Sample flight data
  const defaultFlightData = {
    images: [
      "/assets/img/flights/single-1.jpg",
      "/assets/img/flights/single-2.jpg",
      "/assets/img/flights/single-3.jpg"
    ],
    route: "New York - Los Angeles",
    type: "One Way",
    rating: 4.5,
    reviews: 35,
    takeOffTime: "Sat, 25 Oct | 07:30AM",
    takeOffCode: "JFK",
    landingTime: "Sat, 25 Oct | 09:25AM",
    landingCode: "LAX",
    stops: "1 Stop (STN)",
    stopDuration: "1h 25m",
    airline: "Delta Airlines",
    flightType: "Economy",
    fareType: "Refundable",
    cancellationFee: "$50 / Per Person",
    flightChange: "$50 / Per Person",
    seatsSequence: "$50 Extra Charge",
    inflightFeatures: "Available",
    taxesFees: "$50"
  };

  // Sample about data
  const defaultAboutData = {
    title: "About Delta Airlines",
    content: [
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.",
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text."
    ]
  };

  // Sample sidebar data
  const defaultSidebarData = {
    faqData: [
      {
        question: "What Are The Charges Of Services ?",
        answer: "We denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire odio dignissim quam."
      },
      {
        question: "How Can I Become A Member ?",
        answer: "To become a member, simply sign up on our website and choose your preferred membership plan."
      },
      {
        question: "Can I Upgrade My Plan Any Time ?",
        answer: "Yes, you can upgrade your plan at any time from your account dashboard."
      }
    ],
    contactData: {
      title: "Get A Question?",
      description: "It is a long established fact that a reader will be distracted by the readable content layout.",
      phone: "****** 4567 897",
      email: "<EMAIL>"
    },
    organizerData: {
      title: "Organized By",
      organizerName: "Roltak Travel Agency",
      memberSince: "Member Since 2025",
      avatar: "/assets/img/team/organizer-avatar.jpg"
    },
    bookingData: {
      price: "$450.00",
      originalPrice: "$500.00",
      journeyDate: "9/11/2025",
      journeyDay: "Thursday",
      returnDate: "9/12/2025",
      returnDay: "Friday",
      passengers: "2 Passenger",
      passengerClass: "Business",
      views: "250 Views",
      shares: "4 Share"
    },
    whyBookData: {
      title: "Why Book With Us?",
      features: [
        {
          icon: "Shield",
          title: "Best Price Guarantee",
          description: "We guarantee the best prices for all our services"
        },
        {
          icon: "Headphones",
          title: "24/7 Customer Care",
          description: "Round the clock customer support for your convenience"
        },
        {
          icon: "MapPin",
          title: "Hand Picked Tours & Activities",
          description: "Carefully selected tours and activities for the best experience"
        },
        {
          icon: "Plane",
          title: "Free Travel Insurance",
          description: "Complimentary travel insurance for your peace of mind"
        },
        {
          icon: "Car",
          title: "Comfortable And Hygienic Vehicle",
          description: "Clean and comfortable vehicles for your journey"
        }
      ]
    }
  };

  const flight = { ...defaultFlightData, ...flightData };
  const about = { ...defaultAboutData, ...aboutData };
  const sidebar = { ...defaultSidebarData, ...sidebarData };

  return (
    <div className={`min-h-screen mb-20 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content - Takes 3 columns */}
          <div className="lg:col-span-3 space-y-6">
            {/* Flight Card */}
            <FlightCard
              flightData={flight}
              onPrevious={() => console.log('Previous flight')}
              onNext={() => console.log('Next flight')}
            />
            <hr className='border-gray-200 px-5' />

            {/* About Section */}
            <AboutSection
              title={about.title}
              content={about.content}
            />
            <hr className='border-gray-200 px-5' />

            {/* Inflight Features */}
            <InflightFeatures />
            <hr className='border-gray-200 px-5' />

            {/* Baggage Section */}
            <BaggageSection />
            <hr className='border-gray-200 px-5' />

            {/* Fare Rules */}
            <FareRules />
            <hr className='border-gray-200 px-5' />

            {/* FAQ */}
            <FAQ />
            <hr className='border-gray-200 px-5' />

            <ReviewForm />
          </div>

          {/* Sidebar - Takes 1 column */}
          <div className="lg:col-span-1">
            <Sidebar />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightDetailPage;
