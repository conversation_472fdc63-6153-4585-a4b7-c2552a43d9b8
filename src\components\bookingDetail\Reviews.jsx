'use client';
import ReviewsSection from '@/components/bookingDetail/ReviewsSection';

export default function Reviews() {
  // Event handlers for interactions
  const handleLike = (reviewId, review) => {
    console.log('Liked review:', reviewId, review.name);
  };

  const handleDislike = (reviewId, review) => {
    console.log('Disliked review:', reviewId, review.name);
  };

  const handleComment = (reviewId, review) => {
    console.log('Commented on review:', reviewId, review.name);
  };

  const handleReply = (reviewId, review) => {
    console.log('Replying to review:', reviewId, review.name);
  };

  // Sample data that matches the image
  const sampleReviews = [
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 2,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
      timeAgo: '2 days ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 3,
      name: 'Jesse Sinker',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 4,
      name: 'Sarah Johnson',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face',
      timeAgo: '3 days ago',
      rating: 4,
      comment: 'Great experience overall! The service was excellent and the staff was very helpful. Would definitely recommend to others.',
      likes: 12,
      dislikes: 2,
      comments: 25
    },
    {
      id: 5,
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '5 days ago',
      rating: 5,
      comment: 'Outstanding service! Everything was perfect from start to finish. The attention to detail was remarkable.',
      likes: 20,
      dislikes: 1,
      comments: 35
    },
    {
      id: 6,
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 week ago',
      rating: 4,
      comment: 'Very good experience. The quality was high and the process was smooth. Minor issues but overall satisfied.',
      likes: 8,
      dislikes: 3,
      comments: 18
    }
  ];

  const ratingDistribution = {
  5: 90,  // 90%
  4: 80,  // 80%
  3: 59,  // 59%
  2: 70,  // 70%
  1: 49   // 49%
};

  return (
    <div className=" py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">

          <ReviewsSection
            overallRating={4.5}
            totalReviews={126}
            ratingDistribution={ratingDistribution}
            reviews={sampleReviews}
            onLike={handleLike}
            onDislike={handleDislike}
            onComment={handleComment}
            onReply={handleReply}
            showLoadMore={true}
            initialVisibleReviews={3}
          />
        </div>
      </div>
    </div>
  );
}
