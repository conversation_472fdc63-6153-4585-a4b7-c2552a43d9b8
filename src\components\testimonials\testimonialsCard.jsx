import Image from 'next/image';

const TestimonialCard = ({ name, designation, image, review, rating }) => {
  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <svg
        key={index}
        className={`w-3 h-3 sm:w-4 sm:h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="h-[280px] sm:h-[250px] w-full max-w-[500px] bg-white rounded-4xl sm:rounded-full p-2 sm:p-4 md:p-6 lg:p-8">
      <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 md:gap-6 lg:gap-8">
        {/* Image (Top on sm, Left on md and up) */}
        <div className="flex-shrink-0 w-full sm:w-auto">
          <img
            src={image}
            alt={name}
            width={96}
            height={96}
            className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full object-cover border-4 border-teal-500 mx-auto sm:mx-0"
          />
        </div>

        {/* Content (Below on sm, Right on md and up) */}
        <div className="flex-1 flex flex-col w-full sm:w-auto">
          <div className="flex flex-col">
            <h4 className="font-bold text-base sm:text-lg md:text-xl text-gray-800 mb-1 sm:mb-2">{name}</h4>
            <p className="text-teal-600 text-xs sm:text-sm md:text-base font-medium">{designation}</p>
          </div>
          <p className="text-gray-600 leading-relaxed mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base line-clamp-4">{review}</p>
          <div className="flex gap-1 sm:gap-1.5 md:gap-2">{renderStars(rating)}</div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialCard;