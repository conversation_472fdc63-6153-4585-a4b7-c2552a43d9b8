'use client';
import ReviewsSection from '@/components/bookingDetail/ReviewsSection';

export default function ReviewsDemo() {
  // Event handlers for interactions
  const handleLike = (reviewId, review) => {
    console.log('Liked review:', reviewId, review.name);
  };

  const handleDislike = (reviewId, review) => {
    console.log('Disliked review:', reviewId, review.name);
  };

  const handleComment = (reviewId, review) => {
    console.log('Commented on review:', reviewId, review.name);
  };

  const handleReply = (reviewId, review) => {
    console.log('Replying to review:', reviewId, review.name);
  };

  // Sample data that matches the image
  const sampleReviews = [
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 2,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
      timeAgo: '2 days ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 3,
      name: 'Jesse Sinker',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 4,
      name: 'Sarah Johnson',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face',
      timeAgo: '3 days ago',
      rating: 4,
      comment: 'Great experience overall! The service was excellent and the staff was very helpful. Would definitely recommend to others.',
      likes: 12,
      dislikes: 2,
      comments: 25
    },
    {
      id: 5,
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '5 days ago',
      rating: 5,
      comment: 'Outstanding service! Everything was perfect from start to finish. The attention to detail was remarkable.',
      likes: 20,
      dislikes: 1,
      comments: 35
    },
    {
      id: 6,
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 week ago',
      rating: 4,
      comment: 'Very good experience. The quality was high and the process was smooth. Minor issues but overall satisfied.',
      likes: 8,
      dislikes: 3,
      comments: 18
    }
  ];

  const ratingDistribution = {
    5: 90,  // 90%
    4: 80,  // 80%
    3: 59,  // 59%
    2: 70,  // 70%
    1: 49   // 49%
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-[#0C2C7A] mb-4">Reviews Section Demo</h1>
            <p className="text-gray-600">
              This is a fully functional reviews section component that matches the design from your image. 
              It includes dynamic interactions, rating distributions, and a load more feature.
            </p>
          </div>

          <ReviewsSection
            overallRating={4.5}
            totalReviews={126}
            ratingDistribution={ratingDistribution}
            reviews={sampleReviews}
            onLike={handleLike}
            onDislike={handleDislike}
            onComment={handleComment}
            onReply={handleReply}
            showLoadMore={true}
            initialVisibleReviews={3}
          />

          <div className="mt-12 bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold text-[#0C2C7A] mb-4">Features Included:</h2>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Dynamic overall rating display with stars
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Interactive rating distribution bars
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Individual review cards with user avatars
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Like, dislike, and comment interactions
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Load more functionality
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Responsive design for all screen sizes
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-[#24BDC7] rounded-full mr-3"></span>
                Customizable data through props
              </li>
            </ul>
          </div>

          <div className="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-[#0C2C7A] mb-3">How to Use:</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
{`import ReviewsSection from '@/components/ui/ReviewsSection';

<ReviewsSection
  overallRating={4.5}
  totalReviews={126}
  ratingDistribution={{
    5: 76, 4: 38, 3: 32, 2: 13, 1: 25
  }}
  reviews={reviewsData}
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
