// components/home/<USER>/LocationSelector.jsx
import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlaneDeparture, faPlaneArrival } from '@fortawesome/free-solid-svg-icons';
import locationsData from '../../../app/data/locationsData.json';

// Transform locations data for the component
const LOCATION_DATA = locationsData.map(location => ({
  city: location.city,
  airport: location.airport,
  code: location.code
}));

const LocationSelector = ({
  label,
  location,
  onLocationChange
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeInput, setActiveInput] = useState(''); // 'city' or 'airport'
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const containerRef = useRef(null);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filterSuggestions = (query, type) => {
    if (!query || query.length < 2) {
      setFilteredSuggestions([]);
      return;
    }

    const filtered = LOCATION_DATA.filter(item => {
      if (type === 'city') {
        return item.city.toLowerCase().includes(query.toLowerCase());
      }
      return false;
    }).slice(0, 8); // Limit to 8 suggestions

    setFilteredSuggestions(filtered);
  };

  const handleCityChange = (e) => {
    const value = e.target.value;
    if (onLocationChange) {
      onLocationChange({
        ...location,
        city: value
      });
    }
    setActiveInput('city');
    filterSuggestions(value, 'city');
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (suggestion) => {
    if (onLocationChange) {
      onLocationChange({
        city: suggestion.city,
        airport: ''
      });
    }
    setShowSuggestions(false);
    setFilteredSuggestions([]);
  };

  const handleInputFocus = (inputType) => {
    setIsFocused(true);
    setActiveInput(inputType);
    // Show suggestions if there's already text
    if (inputType === 'city' && location.city) {
      filterSuggestions(location.city, 'city');
      setShowSuggestions(true);
    }
  };

  return (
    <div className="col-span-12 sm:col-span-6 lg:col-span-3 text-[#0C2C7A]" ref={containerRef}>
      <div className={`relative bg-[#E4F7F8] rounded-xl p-1 pb-3 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">{label}</label>
          <FontAwesomeIcon
            icon={label.toLowerCase().includes('from') ? faPlaneDeparture : faPlaneArrival}
            className="text-[#24BDC7] text-sm"
          />
        </div>

        {/* City Input */}
        <div className="flex-1 flex items-center justify-center relative">
          <input
            type="text"
            value={location.city || ''}
            onChange={handleCityChange}
            onFocus={() => handleInputFocus('city')}
            onBlur={() => setTimeout(() => setIsFocused(false), 150)}
            placeholder="Enter city name"
            className="w-full bg-transparent text-sm lg:text-base font-semibold text-[#0C2C7A] placeholder-gray-400 outline-none rounded p-1 transition-colors"
          />
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-0.5 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 lg:max-h-64 overflow-y-auto">
            {filteredSuggestions.map((suggestion, index) => (
              <div
                key={`${suggestion.city}-${suggestion.code}-${index}`}
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-3 lg:px-4 py-1 lg:py-2 hover:bg-[#E4F7F8] cursor-pointer border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="text-base lg:text-lg font-semibold text-[#0C2C7A] truncate">{suggestion.city}</div>
                  </div>
                  <div className="text-xs lg:text-sm font-medium text-[#24BDC7] bg-[#E4F7F8] px-1.5 py-0.5 rounded ml-2 flex-shrink-0">
                    {suggestion.code}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LocationSelector;
