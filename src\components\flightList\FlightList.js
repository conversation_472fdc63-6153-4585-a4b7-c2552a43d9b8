import React from 'react';
import FlightCard from './FlightCard';
import flightData from '../../app/data/flightData.json';

const FlightList = ({ flights = [] }) => {
  // Use flight data from JSON file if none provided
  const flightDataToUse = flights.length > 0 ? flights : flightData;

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-6">
      <div className="mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">Available Flights</h2>
        <p className="text-sm md:text-base text-gray-600">Choose from {flightDataToUse.length} available flights</p>
      </div>
      
      <div className="space-y-3 md:space-y-4">
        {flightDataToUse.map((flight, index) => (
          <FlightCard key={flight.id || index} flight={flight} />
        ))}
      </div>
      
      {flightDataToUse.length === 0 && (
        <div className="text-center py-8 md:py-12">
          <div className="text-gray-500 text-base md:text-lg">No flights found for your search criteria</div>
          <p className="text-gray-400 mt-2 text-sm md:text-base">Try adjusting your search parameters</p>
        </div>
      )}
    </div>
  );
};

export default FlightList;
