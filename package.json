{"name": "nextjs-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.1", "@fortawesome/free-brands-svg-icons": "^7.0.1", "@fortawesome/free-regular-svg-icons": "^7.0.1", "@fortawesome/free-solid-svg-icons": "^7.0.1", "@fortawesome/react-fontawesome": "^3.0.2", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "lucide-react": "^0.542.0", "next": "15.3.4", "next-intl": "^4.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "swiper": "^11.2.10"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}