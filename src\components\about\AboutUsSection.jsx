import React from 'react';
import Image from 'next/image';
import SectionBadge from '../ui/SectionBadge';
import ActionButton from '../ui/ActionButton';

const AboutUsSection = () => {
  return (
    <section className="py-6 sm:py-8 md:py-12 lg:py-16 xl:py-20 bg-[#FDFDFD] text-left">
      <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12 max-w-7xl">
        <SectionBadge text="ABOUT US" isMobile={false} />
        
        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-[#24BDC7] mb-4 sm:mb-6 leading-tight text-left">
          <span className="text-[#0C2C7A]">We Are The World</span> Best Travel Booking <span className="text-[#0C2C7A] block sm:inline">Agency Company</span>
        </h1>
        
        <p className="text-[#757F95] mb-6 sm:mb-8 md:mb-10 lg:mb-12 text-left text-sm sm:text-base md:text-lg leading-relaxed max-w-4xl">
          There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form by injected humour or randomised printer took a galley of type and scrambled it to make a type specimen book. words which don&apos;t look even have suffered alteration in some form by injected.
        </p>
        
        <div className="flex flex-col sm:flex-row justify-start gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
          <div className="relative bg-white p-3 sm:p-4 md:p-5 lg:p-6 rounded-xl sm:rounded-2xl md:rounded-3xl shadow-lg w-full sm:w-1/2 hover:shadow-xl transition-shadow duration-300">
            <div className="absolute left-0 top-0 bottom-0 w-1 sm:w-1.5 h-6 sm:h-8 md:h-10 mt-6 sm:mt-8 md:mt-10 bg-[#24BDC7] rounded-r-full"></div>
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="bg-[#24BDC7] text-white rounded-full w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-14 lg:h-14 flex items-center justify-center flex-shrink-0">
                <Image 
                  src="/assets/img/icon/agent.svg" 
                  alt="Agent" 
                  width={24} 
                  height={24} 
                  className="sm:w-[28px] sm:h-[28px] md:w-[32px] md:h-[32px] lg:w-[38px] lg:h-[38px]" 
                  style={{ filter: 'brightness(0) invert(1)' }} 
                />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm sm:text-base md:text-[14px] font-bold text-[#0C2C7A] mb-1 md:mb-2">Get Your Best Deals</h3>
                <p className="text-[#757F95] text-xs sm:text-sm md:text-[12px]">Take a look at our up of the round shows</p>
              </div>
            </div>
          </div>
          
          <div className="relative bg-white p-3 sm:p-4 md:p-5 lg:p-6 rounded-xl sm:rounded-2xl md:rounded-3xl shadow-lg w-full sm:w-1/2 hover:shadow-xl transition-shadow duration-300">
            <div className="absolute left-0 top-0 bottom-0 w-1 sm:w-1.5 h-6 sm:h-8 md:h-10 mt-6 sm:mt-8 md:mt-10 bg-[#24BDC7] rounded-r-full"></div>
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="bg-[#24BDC7] text-white rounded-full w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-14 lg:h-14 flex items-center justify-center flex-shrink-0">
                <Image 
                  src="/assets/img/icon/deal.svg" 
                  alt="Deal" 
                  width={24} 
                  height={24} 
                  className="sm:w-[28px] sm:h-[28px] md:w-[32px] md:h-[32px] lg:w-[38px] lg:h-[38px]" 
                  style={{ filter: 'brightness(0) invert(1)' }} 
                />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm sm:text-base md:text-[14px] font-bold text-[#0C2C7A] mb-1 md:mb-2">Easy To Booking</h3>
                <p className="text-[#757F95] text-xs sm:text-sm md:text-[12px]">Take a look at our up of the round shows</p>
              </div>
            </div>
          </div>
        </div>
        
        <ActionButton text="Discover More" isMobile={false} />
      </div>
    </section>
  );
};

export default AboutUsSection;