'use client';
import { useState } from 'react';
import { ThumbsUp, ThumbsDown, MessageCircle } from 'lucide-react';

const ReviewsSection = ({
  overallRating = 4.5,
  totalReviews = 126,
  ratingDistribution = {
    5: 60,
    4: 30,
    3: 25,
    2: 10,
    1: 20
  },
  reviews = [],
  onLike,
  onDislike,
  onComment,
  onReply,
  showLoadMore = true,
  initialVisibleReviews = 3
}) => {
  const [visibleReviews, setVisibleReviews] = useState(initialVisibleReviews);
  const [reviewInteractions, setReviewInteractions] = useState({});
  const [showReplyForm, setShowReplyForm] = useState({});

  // Default reviews data if none provided
  const defaultReviews = [
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 2,
      name: 'Erica T. Denio',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
      timeAgo: '2 days ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 3,
      name: 'Jesse Sinker',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    }
  ];

  const reviewsData = reviews.length > 0 ? reviews : defaultReviews;

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  const handleInteraction = (reviewId, type, review) => {
    setReviewInteractions(prev => ({
      ...prev,
      [reviewId]: {
        ...prev[reviewId],
        [type]: !prev[reviewId]?.[type]
      }
    }));

    // Call external handlers if provided
    if (type === 'liked' && onLike) {
      onLike(reviewId, review);
    } else if (type === 'disliked' && onDislike) {
      onDislike(reviewId, review);
    } else if (type === 'commented' && onComment) {
      onComment(reviewId, review);
    }
  };

  const handleReply = (reviewId, review) => {
    setShowReplyForm(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));

    if (onReply) {
      onReply(reviewId, review);
    }
  };

  const loadMoreReviews = () => {
    setVisibleReviews(prev => Math.min(prev + 3, reviewsData.length));
  };

  return (
    <div className=" p-6">
      {/* Reviews Header */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold text-[#0C2C7A] mb-6">Reviews</h3>
        
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Overall Rating */}
          <div className="flex flex-col items-center border border-gray-100 rounded-3xl text-center p-6">
            <div className="text-5xl font-bold text-[#24BDC7] mb-2">{overallRating}</div>
            <div className="flex items-center mb-2">
              {renderStars(Math.floor(overallRating))}
            </div>
            <p className="text-sm text-gray-600">Based On {totalReviews} Reviews</p>
          </div>

          {/* Rating Distribution */}
          <div className="flex-1 max-w-md">
            {[5, 4, 3, 2, 1].map((star) => {
              const percentage = Math.round((ratingDistribution[star] / totalReviews) * 100);
              return (
                <div key={star} className="flex items-center mb-2">
                  <div className="flex items-center w-16">
                    {renderStars(star)}
                  </div>
                  <div className="flex-1 mx-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#24BDC7] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-sm text-gray-600 w-10 text-right">{percentage}%</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Reviews Count */}
      <div className="mb-6">
        <p className="text-[#0C2C7A] font-medium">Showing 1-{Math.min(visibleReviews, reviewsData.length)} Reviews</p>
      </div>

      {/* Individual Reviews */}
      <div className="space-y-6">
        {reviewsData.slice(0, visibleReviews).map((review) => (
          <div key={review.id} className="border-b border-gray-100 pb-6 last:border-b-0">
            <div className="flex gap-4">
              {/* Avatar */}
              <div className="flex-shrink-0">
                <img
                  src={review.avatar}
                  alt={review.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              </div>

              {/* Review Content */}
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{review.name}</h4>
                    <p className="text-sm text-gray-500">{review.timeAgo}</p>
                  </div>
                  <div className="flex items-center">
                    {renderStars(review.rating)}
                  </div>
                </div>

                <p className="text-gray-700 mb-4 leading-relaxed">{review.comment}</p>

                {/* Interaction Buttons */}
                <div className="flex items-center gap-6">
                  <button
                    onClick={() => handleInteraction(review.id, 'liked', review)}
                    className={`flex items-center gap-2 text-sm transition-colors ${
                      reviewInteractions[review.id]?.liked
                        ? 'text-[#24BDC7]'
                        : 'text-gray-500 hover:text-[#24BDC7]'
                    }`}
                  >
                    <ThumbsUp size={16} />
                    <span>{review.likes + (reviewInteractions[review.id]?.liked ? 1 : 0)}</span>
                  </button>

                  <button
                    onClick={() => handleInteraction(review.id, 'disliked', review)}
                    className={`flex items-center gap-2 text-sm transition-colors ${
                      reviewInteractions[review.id]?.disliked
                        ? 'text-red-500'
                        : 'text-gray-500 hover:text-red-500'
                    }`}
                  >
                    <ThumbsDown size={16} />
                    <span>{review.dislikes + (reviewInteractions[review.id]?.disliked ? 1 : 0)}</span>
                  </button>

                  <button
                    onClick={() => handleInteraction(review.id, 'commented', review)}
                    className="flex items-center gap-2 text-sm text-gray-500 hover:text-[#24BDC7] transition-colors"
                  >
                    <MessageCircle size={16} />
                    <span>{review.comments}</span>
                  </button>

                  <button
                    onClick={() => handleReply(review.id, review)}
                    className="text-sm text-gray-500 hover:text-[#24BDC7] transition-colors ml-auto"
                  >
                    Reply
                  </button>
                </div>

                {/* Reply Form */}
                {showReplyForm[review.id] && (
                  <div className="mt-4 ml-4 p-4 bg-gray-50 rounded-lg">
                    <textarea
                      placeholder="Write your reply..."
                      className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#24BDC7] resize-none"
                      rows={3}
                    />
                    <div className="flex justify-end gap-2 mt-3">
                      <button
                        onClick={() => setShowReplyForm(prev => ({ ...prev, [review.id]: false }))}
                        className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        Cancel
                      </button>
                      <button className="px-4 py-2 text-sm bg-[#24BDC7] text-white rounded-md hover:bg-[#1ea5ae] transition-colors">
                        Post Reply
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {showLoadMore && visibleReviews < reviewsData.length && (
        <div className="flex justify-center mt-8">
          <button
            onClick={loadMoreReviews}
            className="bg-[#24BDC7] text-white px-8 py-3 rounded-full font-medium hover:bg-[#1ea5ae] transition-colors"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
};

export default ReviewsSection;
