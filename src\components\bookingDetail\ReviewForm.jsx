import { useState } from "react";
import { Star } from "lucide-react";

export default function ReviewForm() {
  const [rating, setRating] = useState(0);

  return (
    <div className="bg-[#F0FAFC] p-6 rounded-2xl shadow-sm max-w-4xl mx-5">
      {/* Title */}
      <h2 className="text-lg font-semibold text-[#0C2C7A] mb-2">Leave A Review</h2>

      {/* Rating */}
      <p className="text-gray-500 text-sm my-3 font-semibold">Your Rating</p>
      <div className="flex space-x-1 mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={24}
            className={`cursor-pointer ${
              star <= rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
            }`}
            onClick={() => setRating(star)}
          />
        ))}
      </div>

      {/* Form */}
      <form className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Your Name"
            className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#24BDC7]"
          />
          <input
            type="email"
            placeholder="Your Email"
            className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#24BDC7]"
          />
        </div>
        <textarea
          placeholder="Write Your Review"
          rows={4}
          className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#24BDC7]"
        ></textarea>

        <button
          type="submit"
          className="bg-gradient-to-r from-[#24BDC7] to-[#13A1AF] text-white px-6 py-2 rounded-md shadow hover:opacity-90 transition"
        >
          Leave A Review
        </button>
      </form>
    </div>
  );
}
