'use client';
import { useState, useEffect } from 'react';
import TestimonialCard from './testimonialsCard';
import SectionBadge from '../ui/SectionBadge';

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(2); // Default to 2 for desktop

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      designation: 'Designer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 2,
      name: '<PERSON>',
      designation: 'Developer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 3,
      name: '<PERSON>',
      designation: 'Manager',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      review: 'Excellent service and support! The team went above and beyond to ensure our project was completed successfully.',
    },
    {
      id: 4,
      name: 'Miguel Woodworth',
      designation: 'Developer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
  ];

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Mobile: 1 item
        } else if (window.innerWidth < 1024) {
          setItemsPerView(1); // Tablet: 2 items
        } else {
          setItemsPerView(2); // Desktop: 2 items (can increase to 3 if desired)
        }
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= testimonials.length - itemsPerView ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex <= 0 ? testimonials.length - itemsPerView : prevIndex - 1
    );
  };

  return (
    <section className="relative py-10 sm:py-16 lg:py-20 bg-[url('/assets/img/testimonial/bg.jpg')] bg-fixed bg-cover bg-center overflow-hidden">
      <div className="absolute inset-0 bg-black opacity-50"></div>
      <div className="absolute inset-0 opacity-10">
        <div className="absolute bottom-[-30px] sm:bottom-[-40px] lg:bottom-[-60px] left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-5xl sm:text-7xl lg:text-9xl font-bold">
          TAVELO
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <SectionBadge text="TESTIMONIALS" isMobile={false} />
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            What Our Customers Are Saying
            <br />
            About Us?
          </h2>
        </div>

        <div className="relative max-w-full sm:max-w-3xl lg:max-w-5xl mx-auto">
          <div className="flex flex-col justify-center items-center space-y-6">
            {/* Carousel Content */}
            <div className="w-full">
              <div className="flex flex-col space-y-6 w-full overflow-hidden ">
                <div
                  className="flex transition-transform duration-300 ease-in-out"
                  style={{ transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)` }}
                >
                  {testimonials.map((testimonial) => (
                    <div
                      key={testimonial.id}
                      className="w-full flex-shrink-0 px-0 sm:px-2 md:px-4 lg:px-6"
                      style={{ flexBasis: `${100 / itemsPerView}%` }}
                    >
                      <TestimonialCard {...testimonial} />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Navigation Buttons (Bottom for all screens) */}
            <div className="flex justify-between w-full mt-4">
              <button
                onClick={prevTestimonial}
                className="w-10 h-10 sm:w-12 sm:h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
                aria-label="Previous testimonial"
              >
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button
                onClick={nextTestimonial}
                className="w-10 h-10 sm:w-12 sm:h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
                aria-label="Next testimonial"
              >
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex justify-center mt-6 sm:mt-8 lg:mt-10 space-x-2">
            {Array.from({ length: Math.ceil(testimonials.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  Math.floor(currentIndex / itemsPerView) === index ? 'bg-teal-400 w-6 sm:w-8' : 'bg-white/30'
                }`}
                aria-label={`Go to testimonial slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}