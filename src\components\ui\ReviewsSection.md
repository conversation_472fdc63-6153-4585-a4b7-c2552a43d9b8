# ReviewsSection Component

A fully functional and dynamic reviews section component that matches the design from your provided image. This component includes interactive features like like/dislike buttons, comment functionality, rating distributions, and a load more feature.

## Features

- ✅ **Dynamic Overall Rating Display** - Shows average rating with stars
- ✅ **Interactive Rating Distribution** - Visual bars showing rating breakdown
- ✅ **Individual Review Cards** - User avatars, names, ratings, and comments
- ✅ **Like/Dislike/Comment Interactions** - Fully functional with real-time updates
- ✅ **Reply Functionality** - Expandable reply forms for each review
- ✅ **Load More Feature** - Progressive loading of reviews
- ✅ **Responsive Design** - Works on all screen sizes
- ✅ **Customizable Props** - Easy to integrate with your data

## Usage

### Basic Usage

```jsx
import ReviewsSection from '@/components/ui/ReviewsSection';

const MyComponent = () => {
  const reviews = [
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://example.com/avatar.jpg',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'Great service! Highly recommended.',
      likes: 15,
      dislikes: 2,
      comments: 8
    }
    // ... more reviews
  ];

  const ratingDistribution = {
    5: 76,  // Number of 5-star reviews
    4: 38,  // Number of 4-star reviews
    3: 32,  // Number of 3-star reviews
    2: 13,  // Number of 2-star reviews
    1: 25   // Number of 1-star reviews
  };

  return (
    <ReviewsSection
      overallRating={4.5}
      totalReviews={126}
      ratingDistribution={ratingDistribution}
      reviews={reviews}
    />
  );
};
```

### Advanced Usage with Event Handlers

```jsx
import ReviewsSection from '@/components/ui/ReviewsSection';

const MyComponent = () => {
  const handleLike = (reviewId, review) => {
    console.log('Liked review:', reviewId);
    // Update your state or make API call
  };

  const handleDislike = (reviewId, review) => {
    console.log('Disliked review:', reviewId);
    // Update your state or make API call
  };

  const handleComment = (reviewId, review) => {
    console.log('Comment on review:', reviewId);
    // Open comment modal or navigate to comments
  };

  const handleReply = (reviewId, review) => {
    console.log('Reply to review:', reviewId);
    // Handle reply submission
  };

  return (
    <ReviewsSection
      overallRating={4.5}
      totalReviews={126}
      ratingDistribution={ratingDistribution}
      reviews={reviews}
      onLike={handleLike}
      onDislike={handleDislike}
      onComment={handleComment}
      onReply={handleReply}
      showLoadMore={true}
      initialVisibleReviews={3}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `overallRating` | number | 4.5 | The overall rating (0-5) |
| `totalReviews` | number | 126 | Total number of reviews |
| `ratingDistribution` | object | See below | Distribution of ratings by star count |
| `reviews` | array | [] | Array of review objects |
| `onLike` | function | undefined | Callback when user likes a review |
| `onDislike` | function | undefined | Callback when user dislikes a review |
| `onComment` | function | undefined | Callback when user wants to comment |
| `onReply` | function | undefined | Callback when user wants to reply |
| `showLoadMore` | boolean | true | Whether to show the load more button |
| `initialVisibleReviews` | number | 3 | Number of reviews to show initially |

### Review Object Structure

```javascript
{
  id: number,           // Unique identifier
  name: string,         // Reviewer name
  avatar: string,       // Avatar image URL
  timeAgo: string,      // Time since review (e.g., "1 day ago")
  rating: number,       // Rating (1-5)
  comment: string,      // Review text
  likes: number,        // Number of likes
  dislikes: number,     // Number of dislikes
  comments: number      // Number of comments
}
```

### Rating Distribution Object

```javascript
{
  5: number,  // Number of 5-star reviews
  4: number,  // Number of 4-star reviews
  3: number,  // Number of 3-star reviews
  2: number,  // Number of 2-star reviews
  1: number   // Number of 1-star reviews
}
```

## Styling

The component uses Tailwind CSS classes and follows your project's color scheme:
- Primary color: `#0C2C7A` (dark blue)
- Accent color: `#24BDC7` (teal)
- Background: White with subtle shadows

## Examples

Check out these example files:
- `/src/app/[locale]/reviews-demo/page.js` - Full demo page
- `/src/components/examples/ReviewsIntegrationExample.jsx` - Integration example

## Demo

Visit `/reviews-demo` in your application to see the component in action with sample data.

## Dependencies

This component uses:
- `lucide-react` for icons (ThumbsUp, ThumbsDown, MessageCircle)
- `useState` from React for state management
- Tailwind CSS for styling

All dependencies are already included in your project.
