import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

const FlightFilters = ({ onFilterChange, filters = {} }) => {
  const [expandedSections, setExpandedSections] = useState({
    class: true,
    price: true,
    time: true,
    stops: true,
    airlines: true,
    weights: true,
    refundable: true
  });

  const [priceRange, setPriceRange] = useState({
    min: filters.priceMin || 0,
    max: filters.priceMax || 1000
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterChange = (filterType, value, checked = null) => {
    if (onFilterChange) {
      onFilterChange(filterType, value, checked);
    }
  };

  const handlePriceChange = (type, value) => {
    const newRange = { ...priceRange, [type]: parseInt(value) };
    setPriceRange(newRange);
    handleFilterChange('priceRange', newRange);
  };

  const FilterSection = ({ title, section, children }) => (
    <div className="border-b border-gray-200 py-4 text-[#0C2C82]">
      <button
        onClick={() => toggleSection(section)}
        className="flex items-center justify-between w-full text-left"
      >
        <h3 className="text-sm font-semibold ">{title}</h3>
        {expandedSections[section] ? (
          <ChevronUpIcon className="h-4 w-4" />
        ) : (
          <ChevronDownIcon className="h-4 w-4 " />
        )}
      </button>
      {expandedSections[section] && (
        <div className="mt-3 space-y-2">
          {children}
        </div>
      )}
    </div>
  );

  const CheckboxItem = ({ label, count, checked, onChange }) => (
    <label className="flex items-center justify-between cursor-pointer">
      <div className="flex items-center">
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <span className="ml-3 text-sm ">{label}</span>
      </div>
      {count && (
        <span className="text-xs  bg-gray-100 px-2 py-1 rounded">
          ({count})
        </span>
      )}
    </label>
  );

  return (
    <div className="w-64 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="space-y-0">
        {/* Flight Class */}
        <FilterSection title="Flight Class" section="class">
          <CheckboxItem
            label="Business"
            count="20"
            checked={filters.flightClass?.includes('Business')}
            onChange={(e) => handleFilterChange('flightClass', 'Business', e.target.checked)}
          />
          <CheckboxItem
            label="First Class"
            count="15"
            checked={filters.flightClass?.includes('First Class')}
            onChange={(e) => handleFilterChange('flightClass', 'First Class', e.target.checked)}
          />
          <CheckboxItem
            label="Economy"
            count="18"
            checked={filters.flightClass?.includes('Economy')}
            onChange={(e) => handleFilterChange('flightClass', 'Economy', e.target.checked)}
          />
        </FilterSection>

        {/* Flight Price */}
        <FilterSection title="Flight Price" section="price">
          <div className="px-2">
            <div className="flex items-center justify-between text-sm  mb-2">
              <span>Price: ${priceRange.min} - ${priceRange.max}</span>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-xs mb-1">Minimum</label>
                <input
                  type="range"
                  min="0"
                  max="1000"
                  value={priceRange.min}
                  onChange={(e) => handlePriceChange('min', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500
                    [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 
                    [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600 [&::-webkit-slider-thumb]:cursor-pointer
                    [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white [&::-webkit-slider-thumb]:shadow-md
                    [&::-webkit-slider-thumb]:hover:scale-110 [&::-webkit-slider-thumb]:transition-transform
                    [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full 
                    [&::-moz-range-thumb]:bg-blue-600 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-2 
                    [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:shadow-md [&::-moz-range-thumb]:border-none"
                />
              </div>
              <div>
                <label className="block text-xs  mb-1">Maximum</label>
                <input
                  type="range"
                  min="0"
                  max="1000"
                  value={priceRange.max}
                  onChange={(e) => handlePriceChange('max', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500
                    [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 
                    [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600 [&::-webkit-slider-thumb]:cursor-pointer
                    [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white [&::-webkit-slider-thumb]:shadow-md
                    [&::-webkit-slider-thumb]:hover:scale-110 [&::-webkit-slider-thumb]:transition-transform
                    [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full 
                    [&::-moz-range-thumb]:bg-blue-600 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-2 
                    [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:shadow-md [&::-moz-range-thumb]:border-none"
                />
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Flight Time */}
        <FilterSection title="Flight Time" section="time">
          <CheckboxItem
            label="00:00 - 05:59"
            count="20"
            checked={filters.timeSlots?.includes('early-morning')}
            onChange={(e) => handleFilterChange('timeSlots', 'early-morning', e.target.checked)}
          />
          <CheckboxItem
            label="06:00 - 11:59"
            count="15"
            checked={filters.timeSlots?.includes('morning')}
            onChange={(e) => handleFilterChange('timeSlots', 'morning', e.target.checked)}
          />
          <CheckboxItem
            label="12:00 - 17:59"
            count="18"
            checked={filters.timeSlots?.includes('afternoon')}
            onChange={(e) => handleFilterChange('timeSlots', 'afternoon', e.target.checked)}
          />
          <CheckboxItem
            label="18:00 - 23:59"
            count="21"
            checked={filters.timeSlots?.includes('evening')}
            onChange={(e) => handleFilterChange('timeSlots', 'evening', e.target.checked)}
          />
        </FilterSection>

        {/* Flight Stops */}
        <FilterSection title="Flight Stops" section="stops">
          <CheckboxItem
            label="Non Stop"
            count="20"
            checked={filters.stops?.includes('Non Stop')}
            onChange={(e) => handleFilterChange('stops', 'Non Stop', e.target.checked)}
          />
          <CheckboxItem
            label="1 Stop"
            count="15"
            checked={filters.stops?.includes('1 Stop')}
            onChange={(e) => handleFilterChange('stops', '1 Stop', e.target.checked)}
          />
          <CheckboxItem
            label="2 Stop"
            count="18"
            checked={filters.stops?.includes('2 Stop')}
            onChange={(e) => handleFilterChange('stops', '2 Stop', e.target.checked)}
          />
          <CheckboxItem
            label="3 Stop"
            count="5"
            checked={filters.stops?.includes('3 Stop')}
            onChange={(e) => handleFilterChange('stops', '3 Stop', e.target.checked)}
          />
        </FilterSection>

        {/* Airlines */}
        <FilterSection title="Airlines" section="airlines">
          <CheckboxItem
            label="American Airlines"
            count="25"
            checked={filters.airlines?.includes('American Airlines')}
            onChange={(e) => handleFilterChange('airlines', 'American Airlines', e.target.checked)}
          />
          <CheckboxItem
            label="Delta Airlines"
            count="15"
            checked={filters.airlines?.includes('Delta Airlines')}
            onChange={(e) => handleFilterChange('airlines', 'Delta Airlines', e.target.checked)}
          />
          <CheckboxItem
            label="Qatar Airways"
            count="18"
            checked={filters.airlines?.includes('Qatar Airways')}
            onChange={(e) => handleFilterChange('airlines', 'Qatar Airways', e.target.checked)}
          />
          <CheckboxItem
            label="Fly Emirates"
            count="25"
            checked={filters.airlines?.includes('Fly Emirates')}
            onChange={(e) => handleFilterChange('airlines', 'Fly Emirates', e.target.checked)}
          />
          <CheckboxItem
            label="Singapore Airlines"
            count="32"
            checked={filters.airlines?.includes('Singapore Airlines')}
            onChange={(e) => handleFilterChange('airlines', 'Singapore Airlines', e.target.checked)}
          />
        </FilterSection>

        {/* Weights */}
        <FilterSection title="Weights" section="weights">
          <CheckboxItem
            label="25 kg"
            count="20"
            checked={filters.weights?.includes('25')}
            onChange={(e) => handleFilterChange('weights', '25', e.target.checked)}
          />
        </FilterSection>

        {/* Refundable */}
        <FilterSection title="Refundable" section="refundable">
          <CheckboxItem
            label="Non Refundable"
            count="20"
            checked={filters.refundable?.includes('Non Refundable')}
            onChange={(e) => handleFilterChange('refundable', 'Non Refundable', e.target.checked)}
          />
          <CheckboxItem
            label="Refundable"
            count="15"
            checked={filters.refundable?.includes('Refundable')}
            onChange={(e) => handleFilterChange('refundable', 'Refundable', e.target.checked)}
          />
          <CheckboxItem
            label="As Per Rules"
            count="18"
            checked={filters.refundable?.includes('As Per Rules')}
            onChange={(e) => handleFilterChange('refundable', 'As Per Rules', e.target.checked)}
          />
        </FilterSection>
      </div>
    </div>
  );
};

export default FlightFilters;
