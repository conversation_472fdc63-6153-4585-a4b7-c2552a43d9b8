import React, { useState, useMemo } from 'react';
import { AdjustmentsHorizontalIcon, XMarkIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import FlightFilters from './FlightFilters';
import FlightCard from './FlightCard';
import flightData from '../../app/data/flightData.json';

const FlightSearchResults = () => {
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [flightsPerPage] = useState(6);
  const [filters, setFilters] = useState({
    flightClass: [],
    priceRange: { min: 0, max: 1000 },
    timeSlots: [],
    stops: [],
    airlines: [],
    weights: [],
    refundable: []
  });

  const handleFilterChange = (filterType, value, checked) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      
      switch (filterType) {
        case 'priceRange':
          newFilters.priceRange = value;
          break;
        
        case 'flightClass':
        case 'timeSlots':
        case 'stops':
        case 'airlines':
        case 'weights':
        case 'refundable':
          if (checked) {
            newFilters[filterType] = [...prev[filterType], value];
          } else {
            newFilters[filterType] = prev[filterType].filter(item => item !== value);
          }
          break;
        
        default:
          break;
      }
      
      return newFilters;
    });
    
    // Reset to first page when filters change
    setCurrentPage(1);
  };

  const getTimeSlot = (time) => {
    if (!time) return null;
    const hour = parseInt(time.split(':')[0]);
    
    if (hour >= 0 && hour < 6) return 'early-morning';
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 24) return 'evening';
    
    return null;
  };

  const mapRefundStatus = (refund) => {
    if (!refund) return 'Non Refundable';
    
    if (refund.includes('Fully Refunded')) return 'Refundable';
    if (refund.includes('Partially Refunded')) return 'As Per Rules';
    if (refund.includes('Not Refunded')) return 'Non Refundable';
    
    return 'As Per Rules';
  };

  const filteredFlights = useMemo(() => {
    return flightData.filter(flight => {
      // Filter by flight class
      if (filters.flightClass.length > 0) {
        if (!filters.flightClass.includes(flight.flightClass)) {
          return false;
        }
      }

      // Filter by price range
      const finalPrice = flight.price - (flight.price * (flight.discount || 0) / 100);
      if (finalPrice < filters.priceRange.min || finalPrice > filters.priceRange.max) {
        return false;
      }

      // Filter by time slots
      if (filters.timeSlots.length > 0) {
        const departureTimeSlot = getTimeSlot(flight.departureTime);
        if (!filters.timeSlots.includes(departureTimeSlot)) {
          return false;
        }
      }

      // Filter by stops
      if (filters.stops.length > 0) {
        if (!filters.stops.includes(flight.stops)) {
          return false;
        }
      }

      // Filter by airlines (using flight name)
      if (filters.airlines.length > 0) {
        // For now, using flight name as airline filter
        // You can enhance this to match actual airline names
        const hasMatchingAirline = filters.airlines.some(airline => 
          flight.flightName.toLowerCase().includes(airline.toLowerCase().split(' ')[0])
        );
        if (!hasMatchingAirline) {
          return false;
        }
      }

      // Filter by refundable status
      if (filters.refundable.length > 0) {
        const refundStatus = mapRefundStatus(flight.refund);
        if (!filters.refundable.includes(refundStatus)) {
          return false;
        }
      }

      return true;
    });
  }, [filters]);

  // Pagination logic
  const totalPages = Math.ceil(filteredFlights.length / flightsPerPage);
  const indexOfLastFlight = currentPage * flightsPerPage;
  const indexOfFirstFlight = indexOfLastFlight - flightsPerPage;
  const currentFlights = filteredFlights.slice(indexOfFirstFlight, indexOfLastFlight);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top of flight results
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-6">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Mobile Filter Toggle */}
        <div className="lg:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg"
          >
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Filters Sidebar */}
        <div className={`lg:w-64 flex-shrink-0 ${showFilters ? 'block' : 'hidden lg:block'}`}>
          <div>
            {/* Mobile Filter Header */}
            <div className="lg:hidden flex items-center justify-between mb-4 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <XMarkIcon className="h-5 w-5 text-gray-500" />
              </button>
            </div>
            
            <FlightFilters 
              onFilterChange={handleFilterChange}
              filters={filters}
            />
          </div>
        </div>

        {/* Flight Results */}
        <div className="flex-1">
          <div className="mb-4 md:mb-6 bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <p className="text-sm md:text-base font-semibold text-[#0C2C82]">
                Total {flightData.length} Results Found
            </p>
           
          </div>
          
          <div className="space-y-3 md:space-y-4">
            {currentFlights.map((flight, index) => (
              <FlightCard key={flight.id || index} flight={flight} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6 flex items-center justify-center space-x-4">
              <button
                onClick={handlePrevPage}
                disabled={currentPage === 1}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                }`}
              >
                <ChevronLeftIcon className="h-4 w-4" />
                <span>Previous</span>
              </button>

              <div className="flex items-center space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
                  <button
                    key={pageNumber}
                    onClick={() => handlePageChange(pageNumber)}
                    className={`w-10 h-10 rounded-lg border transition-colors ${
                      currentPage === pageNumber
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                    }`}
                  >
                    {pageNumber}
                  </button>
                ))}
              </div>

              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                }`}
              >
                <span>Next</span>
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          )}
          
          {filteredFlights.length === 0 && (
            <div className="text-center py-8 md:py-12">
              <div className="text-gray-500 text-base md:text-lg">
                No flights found for your search criteria
              </div>
              <p className="text-gray-400 mt-2 text-sm md:text-base">
                Try adjusting your filters to see more results
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FlightSearchResults;
