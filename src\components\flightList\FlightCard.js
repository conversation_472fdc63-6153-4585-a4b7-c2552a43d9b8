import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ActionButton from "../ui/ActionButton";
import {
  faPlaneDeparture,
  faPlaneArrival,
} from "@fortawesome/free-solid-svg-icons";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

const FlightCard = ({ flight }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [activeTab, setActiveTab] = useState("baggage");

  const {
    id,
    flightName = "Unknown Airline",
    departureCity = "Unknown",
    departureCode = "UNK",
    arrivalCity = "Unknown",
    arrivalCode = "UNK",
    departureDate = "",
    arrivalDate = "",
    departureTime = "",
    arrivalTime = "",
    duration = "0h 0m",
    rating = 0,
    reviews = 0,
    features = { wifi: false, meal: false },
    baggage = { cabin: "7 kg", checkIn: "20 kg" },
    flightClass = "Economy",
    stops = "Non Stop",
    price = 0,
    discount = 0,
    image = "",
    badge = "",
    refund = "",
  } = flight || {};

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-4">
      {/* Main Flight Info */}
      <div className="p-4 md:p-6">
        {/* Mobile: Vertical Stack | Desktop: Horizontal Layout */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Airline Info - Full width on mobile */}
          <div className="flex items-center space-x-4 w-full md:w-[25%]">
            {/* Airline Logo */}
            <div className="w-10 h-10 md:w-12 md:h-12 bg-gray-100 rounded-sm flex items-center justify-center flex-shrink-0 overflow-hidden">
              {image ? (
                <img 
                  src={image} 
                  alt={`${flightName} logo`} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
              ) : null}
              <div className="text-gray-500 font-bold text-xs md:text-sm transform rotate-45" style={{display: image ? 'none' : 'flex'}}>
                ✈
              </div>
            </div>

            <div className="flex-1">
              <h3 className="text-base md:text-lg font-semibold text-gray-900">{flightName}</h3>
            </div>
          </div>

          {/* Flight Route - Centered on mobile */}
          <div className="flex flex-col md:flex-row md:items-center md:space-x-8 md:flex-1 md:max-w-md md:mx-8 space-y-4 md:space-y-0">
            {/* Departure */}
            <div className="text-center">
              <div className="text-sm md:text-sm font-bold text-[#0C2C82]">
                {departureTime}
              </div>
              <div className="text-xs md:text-xs font-semibold text-[#0C2C82]">
                {departureCode}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {departureCity}
              </div>
            </div>

            {/* Flight Path */}
            <div className="flex-1 relative flex flex-col items-center">
              <div className="flex items-center justify-center">
                <div className="text-xs md:text-sm text-[#0C2C82] px-2 md:px-3 py-1 rounded-full">
                  {stops}
                </div>
              </div>
              <div className="flex items-center mt-2 w-full justify-center">
                <div className="w-4 h-4 md:w-6 md:h-6 text-[#0C2C82] flex items-center justify-center">
                  <FontAwesomeIcon icon={faPlaneDeparture} className="text-xs md:text-sm" />
                </div>
                <div className="flex-1 h-0.5 bg-gray-300 mx-1 md:mx-2 relative">
                  <div className="absolute right-0 top-0 w-1.5 h-1.5 md:w-2 md:h-2 bg-[#0C2C82] rounded-full transform translate-y-[-2px] md:translate-y-[-3px]"></div>
                </div>
                <div className="w-4 h-4 md:w-6 md:h-6 text-[#0C2C82] flex items-center justify-center">
                  <FontAwesomeIcon icon={faPlaneArrival} className="text-xs md:text-sm" />
                </div>
              </div>
              <div className="text-center text-xs md:text-sm text-gray-500 mt-1">
                {duration}
              </div>
            </div>

            {/* Arrival */}
            <div className="text-center">
              <div className="text-sm md:text-sm font-bold text-[#0C2C82]">
                {arrivalTime}
              </div>
              <div className="text-xs md:text-xs font-semibold text-[#0C2C82]">
                {arrivalCode}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {arrivalCity}
              </div>
            </div>
          </div>

          {/* Pricing and Select - Full width on mobile */}
          <div className="flex flex-row md:flex-col justify-between md:items-center md:space-x-4 w-full md:w-auto space-y-2 md:space-y-0">
            <div className="text-center md:text-right">
              <div className="mb-1">
                {discount > 0 && (
                  <span className="text-xs md:text-sm text-gray-500 line-through mr-2">
                    ${price}
                  </span>
                )}
                <span className="text-lg md:text-2xl font-bold text-[#F96768]">
                  ${discount > 0 ? Math.round(price * (1 - discount / 100)) : price}
                </span>
                {/* {discount > 0 && (
                  <span className="text-xs text-green-600 font-semibold ml-2">
                    {discount}% OFF
                  </span>
                )} */}
              </div>
            </div>
            <div className="flex justify-center md:justify-end">
              <ActionButton className="text-xs w-full md:w-auto" onClick={() => console.log('Select flight')} text="Select" />
            </div>
          </div>
        </div>

        {/* Flight Details Section - Responsive */}
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mt-4 pt-4 border-t border-gray-200 space-y-3 md:space-y-0">
          {/* Features Status - Left on mobile, center on desktop */}
          <div className="flex justify-center md:justify-start space-x-2">
            {refund && (
              <span className={`text-xs md:text-sm px-3 py-1 rounded-full ${
                refund === "Fully Refunded" 
                  ? "text-[#0C2C82] " 
                  : refund === "Partially Refunded"
                  ? "text-[#0C2C82] "
                  : "text-[#0C2C82]"
              }`}>
                {refund}
              </span>
            )}
          </div>

          {/* Flight Details Toggle - Right on mobile, right on desktop */}
          <div className="flex justify-center md:justify-end">
            <button
              onClick={toggleDetails}
              className="flex items-center space-x-2 text-[#0C2C82] hover:text-[#24BDC7] font-medium transition-colors"
            >
              <span className="text-sm md:text-base">Flight Details</span>
              <ChevronDownIcon
                className={`w-4 h-4 md:w-5 md:h-5 transition-transform duration-200 ${
                  showDetails ? "rotate-180" : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="border-t flex flex-col lg:flex-row border-gray-200 ">
          {/* Route Info */}
          <div className="p-6 border-b border-gray-200">
            <div className="text-lg font-semibold text-teal-600 mb-4">
              {departureCode} - {arrivalCode}
            </div>

            {/* Airline Details */}
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-10 h-10 bg-gray-100 rounded-sm flex items-center justify-center overflow-hidden">
                {image ? (
                  <img 
                    src={image} 
                    alt={`${flightName} logo`} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                <div className="text-gray-500 font-bold text-xs transform rotate-45" style={{display: image ? 'none' : 'flex'}}>
                  ✈
                </div>
              </div>
              <div>
                <div className="font-semibold text-gray-900">
                  {flightName}
                </div>
                <div className="text-sm text-gray-600">
                  Flight {id}
                </div>
              </div>
            </div>

            {/* Flight Times - Responsive */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 space-y-4 md:space-y-0">
              <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                {/* Departure */}
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-gray-900">
                    {departureTime || "10:00"}
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">{departureDate || "Today"}</div>
                  <div className="font-semibold text-gray-700 text-sm md:text-base">
                    {departureCode}
                  </div>
                  <div className="text-xs text-gray-500">
                    {departureCity}
                  </div>
                </div>

                {/* Flight Path - Mobile: Vertical, Desktop: Horizontal */}
                <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2 md:px-4">
                  <div className="flex justify-center md:justify-start">
                    <div className="text-[#0C2C82] flex items-center justify-center">
                      <FontAwesomeIcon icon={faPlaneDeparture} className="text-sm md:text-base" />
                    </div>
                  </div>
                  <div className="text-center md:text-left">
                    <div className="text-xs md:text-sm font-medium text-gray-600">
                      {stops}
                    </div>
                  </div>
                  <div className="flex justify-center md:justify-start">
                    <div className="w-12 md:w-16 h-0.5 bg-gray-300"></div>
                  </div>
                  <div className="text-center md:text-left">
                    <div className="text-xs md:text-sm text-gray-600">{duration}</div>
                  </div>
                </div>

                {/* Arrival */}
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-gray-900">
                    {arrivalTime || "14:30"}
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">{arrivalDate || "Today"}</div>
                  <div className="font-semibold text-gray-700 text-sm md:text-base">
                    {arrivalCode}
                  </div>
                  <div className="text-xs text-gray-500">
                    {arrivalCity}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="p-6">
            <div className="flex space-x-8 border-b  border-gray-200 mb-4">
              <button
                onClick={() => setActiveTab("baggage")}
                className={`pb-2 px-1 font-medium text-sm ${
                  activeTab === "baggage"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Baggage
              </button>
              <button
                onClick={() => setActiveTab("fare")}
                className={`pb-2 px-1 font-medium text-sm ${
                  activeTab === "fare"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Fare
              </button>
              <button
                onClick={() => setActiveTab("policy")}
                className={`pb-2 px-1 font-medium text-sm ${
                  activeTab === "policy"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Policy
              </button>
            </div>

            {/* Tab Content */}
            {activeTab === "baggage" && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-20 text-[#0C2C82]">
                <div>
                  <div className="font-semibold mb-2 text-sm md:text-base">Flight</div>
                  <div className="text-sm md:text-base">
                    {departureCode} - {arrivalCode}
                  </div>
                </div>
                <div>
                  <div className="font-semibold mb-2 text-sm md:text-base">Cabin Baggage</div>
                  <div className="text-sm md:text-base">{baggage.cabin}</div>
                </div>
                <div>
                  <div className="font-semibold mb-2 text-sm md:text-base">
                    Checked Baggage
                  </div>
                  <div className="text-sm md:text-base">{baggage.checkIn}</div>
                </div>
              </div>
            )}

            {activeTab === "fare" && (
              <div className="rounded-lg text-[#0C2C82] overflow-hidden">
                {/* Header Row */}
                <div className="grid grid-cols-3 gap-20 border-gray-200">
                  <div className="p-3 font-semibold text-sm">Fare Summary</div>
                  <div className="p-3 font-semibold text-sm">Base Fare</div>
                  <div className="p-3 font-semibold text-sm">Tax</div>
                </div>
                
                {/* Adult Row */}
                <div className="grid grid-cols-3 gap-20">
                  <div className="p-3 text-sm ">Adult x 1</div>
                  <div className="p-3 text-sm  font-medium">$5,423</div>
                  <div className="p-3 text-sm  font-medium">$1,000</div>
                </div>
                
                {/* Child Row */}
                <div className="grid grid-cols-3 gap-20">
                  <div className="p-3 text-sm ">Child x 1</div>
                  <div className="p-3 text-sm  font-medium">$3,423</div>
                  <div className="p-3 text-smfont-medium">$1,000</div>
                </div>
                
                {/* Total Row */}
                <div className="grid grid-cols-3 gap-12 bg-[#EEFAFB] rounded-2xl pl-2">
                  <div className="p-3 text-sm font-semibold ">Total (1 Traveler)</div>
                  <div className="p-3"></div>
                  <div className="p-3 text-lg font-bold text-red-500">
                    ${discount > 0 ? Math.round(price * (1 - discount / 100)) : price}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "policy" && (
              <div className="space-y-4 text-[#0C2C82]">
                <div>
                  <div className="mb-2">
                    1. Refund and Date Change are done as per the following policies.
                  </div>
                </div>
                <div>
                  <div className="">
                    2. Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                  </div>
                </div>
                <div>
                  <div className="">
                    3. Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                  </div>
                </div>
                <div>     
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlightCard;
