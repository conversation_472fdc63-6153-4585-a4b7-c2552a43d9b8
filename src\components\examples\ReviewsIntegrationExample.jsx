'use client';
import { useState } from 'react';
import ReviewsSection from '@/components/bookingDetail/ReviewsSection';

const ReviewsIntegrationExample = () => {
  const [reviews, setReviews] = useState([
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 2,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face',
      timeAgo: '2 days ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words.',
      likes: 15,
      dislikes: 5,
      comments: 50
    },
    {
      id: 3,
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      timeAgo: '1 day ago',
      rating: 5,
      comment: 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don\'t look even slightly believable.',
      likes: 15,
      dislikes: 5,
      comments: 50
    }
  ]);

  // Handle like action
  const handleLike = (reviewId, review) => {
    setReviews(prevReviews => 
      prevReviews.map(r => 
        r.id === reviewId 
          ? { ...r, likes: r.likes + 1 }
          : r
      )
    );
    console.log(`User liked review by ${review.name}`);
  };

  // Handle dislike action
  const handleDislike = (reviewId, review) => {
    setReviews(prevReviews => 
      prevReviews.map(r => 
        r.id === reviewId 
          ? { ...r, dislikes: r.dislikes + 1 }
          : r
      )
    );
    console.log(`User disliked review by ${review.name}`);
  };

  // Handle comment action
  const handleComment = (reviewId, review) => {
    console.log(`User wants to comment on review by ${review.name}`);
    // You can implement comment modal or navigation here
  };

  // Handle reply action
  const handleReply = (reviewId, review) => {
    console.log(`User wants to reply to review by ${review.name}`);
    // Reply form is already handled in the component
  };

  const ratingDistribution = {
    5: 76,
    4: 38,
    3: 32,
    2: 13,
    1: 25
  };

  return (
    <div className="space-y-8">
      {/* Product/Service Info */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h1 className="text-2xl font-bold text-[#0C2C7A] mb-4">Amazing Travel Package</h1>
        <p className="text-gray-600 mb-4">
          Experience the best of our travel services with this comprehensive package that includes 
          flights, hotels, and guided tours.
        </p>
        <div className="flex items-center gap-4">
          <span className="text-3xl font-bold text-[#24BDC7]">$1,299</span>
          <span className="text-gray-500 line-through">$1,599</span>
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">
            19% OFF
          </span>
        </div>
      </div>

      {/* Reviews Section */}
      <ReviewsSection
        overallRating={4.5}
        totalReviews={126}
        ratingDistribution={ratingDistribution}
        reviews={reviews}
        onLike={handleLike}
        onDislike={handleDislike}
        onComment={handleComment}
        onReply={handleReply}
        showLoadMore={true}
        initialVisibleReviews={3}
      />

      {/* Additional Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-[#0C2C7A] mb-4">Leave Your Review</h3>
        <button className="bg-[#24BDC7] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#1ea5ae] transition-colors">
          Write a Review
        </button>
      </div>
    </div>
  );
};

export default ReviewsIntegrationExample;
